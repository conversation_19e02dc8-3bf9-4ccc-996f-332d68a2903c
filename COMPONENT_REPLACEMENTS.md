# @nuxt/ui-pro 组件替换说明

本文档说明了如何将 @nuxt/ui-pro 的特有组件替换为本地自定义组件。

## 已创建的替换组件

### 1. UPageSection (`app/components/UPageSection.vue`)

**原组件**: `@nuxt/ui-pro` 的 `UPageSection`
**功能**: 页面区块容器，支持标题、描述、内容等插槽
**主要属性**:
- `title`: 区块标题
- `description`: 区块描述
- `headline`: 标题上方的小标题
- `orientation`: 布局方向 ('horizontal' | 'vertical')
- `reverse`: 是否反转布局
- `ui`: 自定义样式配置

**插槽**:
- `default`: 默认内容
- `leading`: 前置内容
- `headline`: 标题上方内容
- `title`: 标题内容
- `description`: 描述内容
- `body`: 主体内容
- `footer`: 底部内容
- `links`: 链接区域

### 2. UPageAccordion (`app/components/UPageAccordion.vue`)

**原组件**: `@nuxt/ui-pro` 的 `UPageAccordion`
**功能**: 手风琴折叠面板组件
**主要属性**:
- `items`: 折叠项数组
- `trailingIcon`: 尾部图标
- `labelKey`: 标签字段名
- `type`: 展开类型 ('single' | 'multiple')
- `defaultValue`: 默认展开项
- `modelValue`: 当前展开项
- `ui`: 自定义样式配置

**插槽**:
- `leading`: 每项的前置内容
- `body`: 每项的主体内容
- `trailing`: 每项的尾部内容

### 3. UError (`app/components/UError.vue`)

**原组件**: `@nuxt/ui-pro` 的 `UError`
**功能**: 错误页面组件
**主要属性**:
- `error`: 错误对象 (NuxtError)
- `ui`: 自定义样式配置

**插槽**:
- `default`: 默认内容
- `statusCode`: 状态码内容
- `statusMessage`: 状态消息内容
- `message`: 错误消息内容
- `links`: 链接区域

### 4. UMain (`app/components/UMain.vue`)

**原组件**: `@nuxt/ui-pro` 的 `UMain`
**功能**: 主内容区域容器
**主要属性**:
- `as`: HTML 标签类型 (默认 'main')
- `ui`: 自定义样式配置

### 5. UPage (`app/components/UPage.vue`)

**原组件**: `@nuxt/ui-pro` 的 `UPage`
**功能**: 页面容器组件
**主要属性**:
- `ui`: 自定义样式配置

### 6. UContentSearch (`app/components/UContentSearch.vue`)

**原组件**: `@nuxt/ui-pro` 的 `LazyUContentSearch`
**功能**: 内容搜索组件，支持键盘快捷键
**主要属性**:
- `files`: 搜索文件数组
- `navigation`: 导航数据
- `shortcut`: 键盘快捷键 (默认 'meta_k')
- `links`: 快速链接
- `fuse`: 搜索配置

**功能特性**:
- 支持 Cmd+K 快捷键打开搜索
- 模糊搜索文件标题、描述和路径
- 显示搜索结果和快速链接

### 7. UUser (`app/components/UUser.vue`)

**原组件**: `@nuxt/ui-pro` 的 `UUser`
**功能**: 用户信息显示组件
**主要属性**:
- `name`: 用户名
- `description`: 用户描述
- `avatar`: 头像配置 `{ src, alt }`
- `size`: 尺寸 ('xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl')
- `orientation`: 布局方向 ('horizontal' | 'vertical')
- `color`: 颜色主题
- `variant`: 变体样式 ('solid' | 'outline' | 'soft' | 'ghost' | 'link')

**插槽**:
- `default`: 默认内容
- `avatar`: 头像内容
- `name`: 姓名内容
- `description`: 描述内容

### 8. UColorModeAvatar (`app/components/UColorModeAvatar.vue`)

**原组件**: `@nuxt/ui-pro` 的 `UColorModeAvatar`
**功能**: 支持深色/浅色模式的头像组件
**主要属性**:
- `light`: 浅色模式头像
- `dark`: 深色模式头像
- `alt`: 替代文本
- `size`: 尺寸 ('xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl')

**功能特性**:
- 根据当前颜色模式自动切换头像
- 支持回退到默认图标

### 9. UFooter (`app/components/UFooter.vue`)

**原组件**: `@nuxt/ui-pro` 的 `UFooter`
**功能**: 页脚组件
**主要属性**:
- `ui`: 自定义样式配置

**插槽**:
- `default`: 默认内容
- `left`: 左侧内容
- `center`: 中间内容
- `right`: 右侧内容

## 使用示例

### UPageSection 使用示例

```vue
<UPageSection
  title="关于我们"
  description="这是关于我们的描述"
  orientation="horizontal"
  :ui="{
    container: 'py-16',
    title: 'text-3xl font-bold',
    description: 'text-gray-600'
  }"
>
  <template #leading>
    <UIcon name="i-lucide-info" />
  </template>
  
  <div>
    <!-- 主要内容 -->
  </div>
  
  <template #links>
    <UButton to="/contact">联系我们</UButton>
  </template>
</UPageSection>
```

### UPageAccordion 使用示例

```vue
<UPageAccordion
  :items="faqItems"
  trailing-icon="i-lucide-plus"
  type="single"
  :ui="{
    item: 'border rounded-lg',
    trigger: 'p-4 hover:bg-gray-50'
  }"
>
  <template #body="{ item }">
    <div class="p-4">
      {{ item.content }}
    </div>
  </template>
</UPageAccordion>
```

### UUser 使用示例

```vue
<UUser
  name="John Doe"
  description="Frontend Developer"
  :avatar="{ src: '/avatar.jpg', alt: 'John Doe' }"
  size="lg"
  orientation="horizontal"
  color="primary"
  variant="solid"
/>
```

### UColorModeAvatar 使用示例

```vue
<UColorModeAvatar
  light="/avatar-light.jpg"
  dark="/avatar-dark.jpg"
  alt="User Avatar"
  size="xl"
  class="ring ring-primary"
/>
```

### UFooter 使用示例

```vue
<UFooter>
  <template #left>
    <p>&copy; 2024 My Company</p>
  </template>

  <template #right>
    <UButton variant="ghost" size="sm">Privacy</UButton>
    <UButton variant="ghost" size="sm">Terms</UButton>
  </template>
</UFooter>
```

## 迁移步骤

1. **移除 @nuxt/ui-pro 依赖**
   ```bash
   npm uninstall @nuxt/ui-pro
   ```

2. **更新 nuxt.config.ts**
   - 移除 `@nuxt/ui-pro` 模块
   - 移除 `uiPro` 配置

3. **替换组件引用**
   - 将所有 `UPageSection` 替换为新的本地组件
   - 将所有 `UPageAccordion` 替换为新的本地组件
   - 将所有 `UError` 替换为新的本地组件
   - 将所有 `UMain` 替换为新的本地组件
   - 将所有 `UPage` 替换为新的本地组件
   - 将所有 `LazyUContentSearch` 替换为 `UContentSearch`
   - 将所有 `UUser` 替换为新的本地组件
   - 将所有 `UColorModeAvatar` 替换为新的本地组件
   - 将所有 `UFooter` 替换为新的本地组件

4. **调整样式和配置**
   - 根据需要调整 `ui` 属性配置
   - 确保所有插槽和属性正确传递

## 注意事项

1. 这些组件是基于 @nuxt/ui 的基础组件构建的
2. 样式系统使用 Tailwind CSS
3. 组件支持响应式设计
4. 所有组件都支持 TypeScript
5. 组件遵循 Vue 3 Composition API 规范

## 已修复的文件

- `app/pages/index.vue`
- `app/pages/about.vue`
- `app/pages/projects.vue`
- `app/pages/speaking.vue`
- `app/pages/blog/index.vue`
- `app/pages/blog/[...slug].vue`
- `app/components/landing/Hero.vue`
- `app/components/landing/About.vue`
- `app/components/landing/WorkExperience.vue`
- `app/components/landing/Blog.vue`
- `app/components/landing/Testimonials.vue`
- `app/components/landing/FAQ.vue`
- `app/app.vue`
- `app/error.vue`
- `app.config.ts`
- `app/assets/css/main.css`
