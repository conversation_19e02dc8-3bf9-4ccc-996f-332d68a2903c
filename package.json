{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "nuxt typecheck"}, "dependencies": {"@iconify-json/lucide": "^1.2.54", "@iconify-json/simple-icons": "^1.2.41", "@nuxt/content": "^3.6.1", "@nuxt/image": "^1.10.0", "@nuxt/ui": "^3.2.0", "@vueuse/nuxt": "^13.4.0", "better-sqlite3": "^12.2.0", "motion-v": "^1.3.1", "nuxt": "^3.17.6", "nuxt-og-image": "^5.1.8"}, "devDependencies": {"@nuxt/eslint": "^1.4.1", "eslint": "^9.30.0", "eslint-config-prettier": "^10.1.5", "prettier-eslint": "^16.4.2", "typescript": "^5.8.3", "vue-tsc": "^2.2.10"}, "resolutions": {"unimport": "4.1.1"}, "pnpm": {"onlyBuiltDependencies": ["better-sqlite3", "sharp"], "ignoredBuiltDependencies": ["@parcel/watcher", "esbuild", "vue-demi"]}, "packageManager": "pnpm@10.12.4"}