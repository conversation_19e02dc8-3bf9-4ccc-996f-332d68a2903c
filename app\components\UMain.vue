<script setup lang="ts">
interface Props {
  ui?: Record<string, any>
  class?: any
  as?: string
}

const props = withDefaults(defineProps<Props>(), {
  ui: () => ({}),
  as: 'main'
})

defineSlots<{
  default?: any
}>()

const rootClasses = computed(() => {
  return [
    'flex-1 min-h-0',
    props.ui?.root,
    props.class
  ].filter(Boolean).join(' ')
})
</script>

<template>
  <component :is="as" :class="rootClasses">
    <slot />
  </component>
</template>
